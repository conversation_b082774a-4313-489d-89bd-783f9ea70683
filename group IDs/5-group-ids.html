<!DOCTYPE html>
<html lang="ru-RU">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
        <meta name="theme-color" content="#3f51b5">
        <meta name="csrf-param" content="_csrf-frontend">
<meta name="csrf-token" content="GMSIFEK8uIVHTALH_9IKA5bV56umJ7ZaGjPWYSzVEvRX9sx8L_KJ6j0cb6K3jWlx1bK4m8x28BBQRJ4-RYZ7xQ==">
        <title>Получить направление </title>
        <link rel="icon" type="image/png" href="/favicon.png" />

        <link rel="apple-touch-icon" href="/images/apple-icons/apple-touch-icon.png" />
        <link rel="apple-touch-icon" sizes="57x57" href="/images/apple-icons/apple-touch-icon-57x57.png" />
        <link rel="apple-touch-icon" sizes="72x72" href="/images/apple-icons/apple-touch-icon-72x72.png" />
        <link rel="apple-touch-icon" sizes="76x76" href="/images/apple-icons/apple-touch-icon-76x76.png" />
        <link rel="apple-touch-icon" sizes="114x114" href="/images/apple-icons/apple-touch-icon-114x114.png" />
        <link rel="apple-touch-icon" sizes="120x120" href="/images/apple-icons/apple-touch-icon-120x120.png" />
        <link rel="apple-touch-icon" sizes="144x144" href="/images/apple-icons/apple-touch-icon-144x144.png" />
        <link rel="apple-touch-icon" sizes="152x152" href="/images/apple-icons/apple-touch-icon-152x152.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-icons/apple-touch-icon-180x180.png" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.2/font/bootstrap-icons.css" rel="stylesheet" crossorigin="anonymous" integrity="sha384-b6lVK+yci+bfDmaY1u0zE8YYJt0TZxLEAFyYSLHId4xoVvsrQu3INevFKo+Xir8e">
<link href="/assets/c9c251a7/css/bootstrap.css?v=1681625203" rel="stylesheet">
<link href="/css/password-toggle/style.css?v=1689614760" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.3.5/jquery.fancybox.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css?family=Roboto:400,500,700&amp;display=swap&amp;subset=cyrillic" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&amp;display=swap&quot; rel=&quot;stylesheet" rel="stylesheet">
<link href="/css/loader.css?v=1674925792" rel="stylesheet">
<link href="/css/odometer-theme-plaza.css?v=1674925792" rel="stylesheet">
<link href="/css/slick.css?v=1674925792" rel="stylesheet">
<link href="/css/slick-theme.css?v=1674925792" rel="stylesheet">
<link href="/css/alert.css?v=1693327486" rel="stylesheet">
<link href="/css/jquery.jscrollpane.lozenge.css?v=1674925792" rel="stylesheet">
<link href="/css/jquery.jscrollpane.css?v=1674925792" rel="stylesheet">
<link href="/css/nice-select.css?v=1674925792" rel="stylesheet">
<link href="/css/fancybox/core.css?v=1674925792" rel="stylesheet">
<link href="/css/fancybox/jquery.fancybox.css?v=1674925792" rel="stylesheet">
<link href="/css/bootstrap-datepicker.css?v=1674925792" rel="stylesheet">
<link href="/css/style.css?v=1746030305" rel="stylesheet">
<link href="/css/themes/summer.css?v=1748841651" rel="stylesheet">        <style>
            .btn.btn-outline-indigo {
                border-width: 1px;
                line-height: 1.45;
            }

            .contacts-wrap .accompanying-files__input-doc label span {
                padding: 6px 15px;
            }

            .hide-all-page-to-behind-transparent-block {
                position: fixed;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;
                z-index: 9999;
            }        
            .attendance .calendar .holiday {
                background: rgba(255, 178, 178, 1);
            }

            .attendance .calendar .visit {
                background: rgba(255, 255, 255, 1);
            }

            .attendance .calendar .weekend {
                background: rgba(255, 229, 229, 1);
            }

            .attendance .calendar td {
                border: 1px solid #DEDEDE;
                background: #E9E9E9;
            }

            .attendance .calendar th {
                border-bottom: 1px solid #DEDEDE;
            }

            .contacts-wrap .form-group label.buttons-bar__add {
                margin-bottom: auto;
                color: #D8D8D8;
            }

            .contacts-wrap .btn-file-close {
                flex: none;
            }

            .contacts-wrap .btn-file-input {
                flex: 3;
            }

            .contacts-wrap .file-name-description {
                width: 100%;
            }

            .contacts-wrap .file-name-description a:before {
                width: 20px;
            }

            .contacts-wrap .file-name-description {
                display: flex;
            }

            .contacts-wrap .file-name-description a {
                flex: 3;
            }
            .watermark-background {
                background-image: url("/ru/site/watermark");
            }

            .g-list-view-custom .g-list-item:last-child {
                margin-bottom: 0;
            }

            .g-list-view-custom .g-list-item:first-child {
                padding-top: 1rem;
            }

            .password-requirements-block .requirement {
                color: red;
                font-size: .8125rem;
                position: relative;
                padding-left: 15px;
            }

            .password-requirements-block .requirement:before, .password-requirements-block .requirement:after {
                position: absolute;
                color: red;
                left: 5px;
                top: 3px;
                content: ' ';
                height: 13px;
                width: 2px;
                background-color: red;
            }
            .password-requirements-block .requirement:before {
                transform: rotate(45deg);
            }
            .password-requirements-block .requirement:after {
                transform: rotate(-45deg);
            }

            .password-requirements-block .requirement .wrap-success {
                position: absolute;
                top: 11px;
                left: 0;
            }

            .requirement .wrap-success .check-sign {
                position: absolute;
                background: green;
                display: none;
            }

            #check-part-1.check-sign {
                width: 6px;
                height: 2px;
                transform: rotate(45deg);
            }

            #check-part-2.check-sign {
                width: 10px;
                height: 2px;
                transform: rotate(-45deg);
                left: 3px;
                top: -2px;
            }

            .is-valid.requirement:before, .is-valid.requirement:after {
                display: none;
            }

            .is-valid.requirement {
                color: green;
            }
            .is-valid.requirement .wrap-success .check-sign {
                display: block;
            }

            .login-form label.small-font {
                font-size: 1rem;
            }

            .login-form .checkbox-list {
                width: 40%;
            }

            @media (max-width: 992px) {
                .login-form .checkbox-list {
                    width: 60%;
                }
                .login-form label.small-font {
                    font-size: 1.2rem;
                }
            }

            @media (max-width: 576px) {
                .login-form .checkbox-list {
                    width: 100%;
                }
                .login-form label.small-font {
                    font-size: 1.2rem;
                }
            }
        </style>
    </head>
    <body data-lang="ru" data-zoom="15">
                    <!-- Yandex.Metrika counter --> <script type="text/javascript" > (function (m, e, t, r, i, k, a) {
                    m[i] = m[i] || function () {
                        (m[i].a = m[i].a || []).push(arguments)
                    };
                    m[i].l = 1 * new Date();
                    k = e.createElement(t), a = e.getElementsByTagName(t)[0], k.async = 1, k.src = r, a.parentNode.insertBefore(k, a)
                })(window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");
                ym(61595896, "init", {clickmap: true, trackLinks: true, accurateTrackBounce: true, webvisor: true});</script> <noscript><div><img src="https://mc.yandex.ru/watch/61595896" style="position:absolute; left:-9999px;" alt="" /></div></noscript> <!-- /Yandex.Metrika counter -->
                    <div class="body-wrapper">
                                                    <header >
    <div class="sub-header">
        <div class="container">
                        <div class="d-flex">
                                <div class="dropdown mobile-dropdown align-self-center">
                    <a class="dropdown-info dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                       data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="sometext">Полезная информация</span>                    </a>
                    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                                <a class="dropdown-item" href="https://indigo24.kz/ru/faq/19" target="_blank">Как зачислиться в дошкольную организацию</a>                        <a class="dropdown-item" href="https://indigo24.kz/ru/faq/48" target="_blank">Как распределяются свободные места</a>                        <a class="dropdown-item" href="https://indigo24.kz/ru/faq" target="_blank">Частые вопросы и ответы</a>                        <a class="dropdown-item" href="https://adilet.zan.kz/rus" target="_blank">Нормативно-правовая база</a>                        <a class="dropdown-item" href="https://indigo24.kz/ru/faq" target="_blank">База знаний</a>                        <a href="#" onclick="jivo_api.open(); return false;"
                           class="dropdown-item">Техническая поддержка</a>
                    </div>
                </div>
                                    <div id="clock" class="clock-block ml-auto align-self-center">
                        Эталонное время сервера 230732                        :
                        <span id="clock-text">2025.08.14 05:43</span>
                    </div>
                                <div class="ml-auto align-self-center city-seletc-block">
	<a href="#" class="city-seletc" data-toggle="modal" data-target="#cityModal">
		<span class="d-none d-sm-block">г.Сатпаев</span>
	</a>
    </div>

                    <ul class="lang-select align-self-center">
                    <li >
                <a onclick="app.setLanguage(this.pathname); return false;" href="/kz/reserv/get/organization/group">Қазақша</a>
            </li>
                    <li class="active">
                <a onclick="app.setLanguage(this.pathname); return false;" href="/ru/reserv/get/organization/group">Русский</a>
            </li>
            </ul>
            </div>
        </div>
    </div>
        <div class="main-header">
        <div class="container">
            <nav class="navbar navbar-expand-lg navbar-light nav-indigo">
                <a href="#" class="btn-mobile-menu navbar-toggle" data-toggle="offcanvas" data-target="#myNavmenu"
                   data-canvas="body"></a>
                <div id="mobileMenu" class="mobile-menu">
                    <div class="menu-header">
                        <span class="close-menu"></span>
                        <h4>Навигация</h4>
                    </div>
                    <div class="menu-body">
                        <ul id="menu-list">
                            <li><a href="http://indigo24.kz/?r_off=1">Главная</a></li>
                            <li>
                                <a href="https://www.instagram.com/bilim_webline/?hl=ru">Новости</a>
                            </li>
                            <li><a class="" href="/ru/cabinet/personal/login">Личный кабинет</a></li>
                            <li><a class="text-warning close-menu" href="/ru/callback-form" data-toggle="modal" data-target="#feedbackModal">Сообщить о коррупции</a></li>
                            <li><a data-toggle="collapse" href="#collapseMenu1" role="button" aria-expanded="false"
                                   aria-controls="collapseExample"
                                   class="menu-collapse collapsed">Услуги</a>
                                <ul class="collapse" id="collapseMenu1">
                                    <li>
                                        <a href="/ru/list">Реестр дошкольных организаций</a>
                                    </li>
                                    <li>
                                        <a href="/ru/garden/list?map=1">Карта дошкольных организаций</a>
                                    </li>
                                    <li>
                                                                                <a href="/ru/information/registry">Свободные места</a>
                                    </li>
                                    <li>
                                        <a href="/ru/queue">Очереди заявлений</a>
                                    </li>
                                    <li>
                                        <a href="/ru/statement/step-1">Подача заявления в очередь</a>
                                    </li>
                                    <li>
                                                                                <a href="/ru/information/direction">Получение путевки на зачисление</a>
                                    </li>
                                    <li>
                                        <a href="/ru/request/check">Проверка номера в очереди</a>
                                    </li>
                                </ul>
                            </li>

                            <li>
                                <a href="#" onclick="jivo_api.open(); return false;">
                                    Техническая поддержка                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <a class="navbar-brand main-logo" href="/ru" aria-label="Indig">
                    <img src="/images/logo.svg"/>
                </a>
                <div class="collapse navbar-collapse navbar-indigo" id="navbarSupportedContent">
                    <ul class="navbar-nav mr-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="http://indigo24.kz/?r_off=1">Главная</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"
                               href="/ru/list">Дошкольные организации                                <span class="sr-only">(current)</span></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"
                               href="/ru/statement/step-1">Встать в очередь</a>
                        </li>
                        <li class="nav-item">
                                                        <a class="nav-link"
                               href="/ru/information/registry">Получить направление</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"
                               href="/ru/request/check">Проверить очередь</a>
                        </li>
                    </ul>
                                                                    <div class="user-header dropdown">
                            <a href="#" class="dropdown-toggle" id="dropdownUserInfo" data-toggle="dropdown">
                                <div class="user-view-header d-flex align-items-center">
                                    <div class="user-avatar">
                                        <img class="img-fluid  img-circular" src="/images/user-no-avatar.png" alt="">                                    </div>
                                    <div class="user-info-header">
                                        <div class="user-name">
                                            <span class="user-panel-text">МАХАТОВА</span>
                                            <span class="user-panel-text ml-1">АИДА</span>
                                        </div>
                                        <div class="user-iin">
                                            ИИН ************                                        </div>
                                    </div>
                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-right user--menu-list" x-placement="bottom-end"><li><a href="/ru/cabinet/event-history" class="dropdown-item">Лента событий</a></li>
<li><a href="/ru/cabinet/request/list" class="dropdown-item">Мои заявления</a></li>
<li><a href="/ru/cabinet/reserv/list" class="dropdown-item">Мои направления</a></li>
<li><a href="/ru/cabinet/contracts/index" class="dropdown-item">Мои договоры</a></li>
<li><a href="/ru/cabinet/attendance/index" class="dropdown-item">Табеля посещаемости</a></li>
<li><a href="/ru/cabinet/profile/index" class="dropdown-item">Анкета</a></li>
<li><a href="/ru/cabinet/conventions" class="dropdown-item">Соглашения и ограничения</a></li>
<li><a href="/ru/cabinet/settings" class="dropdown-item">Настройки</a></li>
<li><a class="dropdown-item" href="/ru/cabinet/logout" data-method="post">Выйти</a></li></ul>
                        </div>
                                    </div>
                                    <a href="/ru/cabinet/request/list" class="btn-mobile-login"></a>
                            </nav>
        </div>
    </div>
</header>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
<!-- Modals -->
<div class="modal fade" id="cityModal" tabindex="-1" role="dialog" aria-labelledby="cityModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel"><span class="sometext">Выберите город</span></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="min-height: 300px;">
                <form>
                    <div class="form-group">
                        <input type="text" class="form-control" id="search_city" placeholder="Название населенного пункта">
                    </div>
                </form>
                <div class="accordion accordion-city" id="accordionCity">
                </div>
                <ul class="modal-help-link">
                    <li><a href="#">Зачем нужно выбирать город или райцентр?</a></li>
                    <li><a href="#">В списке нет города или райцентра, где я живу, что делать?</a></li>
                </ul>
                <div class="geo-loading city-loading" style="display: none;">
                    <div class="loader"></div>
                    <p>Загрузка <span id="city-name-change"></span></p>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var globalCitiesList = [{"Name":"Города республиканского значения","cities":[{"Id":1,"Type":2,"Name":"г.Астана","Url":"https:\/\/indigo-nursultan.e-orda.kz\/ru","isHide":0},{"Id":3,"Type":2,"Name":"г.Шымкент","Url":"https:\/\/indigo-shymkent.e-orda.kz\/ru","isHide":0}]},{"Name":"Атырауская область","cities":[{"Id":23,"Type":2,"Name":"г.Атырау","Url":"https:\/\/indigo-atyrau.e-orda.kz\/ru","isHide":0},{"Id":24,"Type":2,"Name":"г.Кульсары","Url":"https:\/\/indigo-kulsary-atyrau.e-orda.kz\/ru","isHide":0},{"Id":120,"Type":3,"Name":"Жылыойский район","Url":"https:\/\/indigo-zhylyoi-atyrau.e-orda.kz\/ru","isHide":0},{"Id":3882,"Type":3,"Name":"Индерский район","Url":"https:\/\/indigo-inder-atyrau.e-orda.kz\/ru","isHide":0},{"Id":37,"Type":3,"Name":"Кзылкогинский район","Url":"https:\/\/indigo-kyzylkoga-atyrau.e-orda.kz\/ru","isHide":0},{"Id":38,"Type":3,"Name":"Курмангазинский район","Url":"https:\/\/indigo-kurmangazy-atyrau.e-orda.kz\/ru","isHide":0},{"Id":39,"Type":3,"Name":"Макатский район","Url":"https:\/\/indigo-makat-atyrau.e-orda.kz\/ru","isHide":0},{"Id":40,"Type":3,"Name":"Махамбетский район","Url":"https:\/\/indigo-makhambet-atyrau.e-orda.kz\/ru","isHide":0},{"Id":27,"Type":4,"Name":"Аккизтогайский сельский округ","Url":"https:\/\/indigo-akkiiztogai-atyrau.e-orda.kz\/ru","isHide":0},{"Id":123,"Type":4,"Name":"Аккистауский сельский округ","Url":"https:\/\/indigo-akkistau-atyrau.e-orda.kz\/ru","isHide":0},{"Id":28,"Type":4,"Name":"Жана Каратонский сельский округ","Url":"https:\/\/indigo-zhanakaraton-atyrau.e-orda.kz\/ru","isHide":0},{"Id":128,"Type":4,"Name":"Жанбайский сельский округ","Url":"https:\/\/indigo-zhanbai-atyrau.e-orda.kz\/ru","isHide":0},{"Id":25,"Type":4,"Name":"Жемский сельский округ","Url":"https:\/\/indigo-zhem-atyrau.e-orda.kz\/ru","isHide":0},{"Id":129,"Type":4,"Name":"Зинеденский сельский округ","Url":"https:\/\/indigo-zaburun-atyrau.e-orda.kz\/ru","isHide":0},{"Id":124,"Type":4,"Name":"Исатайский сельский округ","Url":"https:\/\/indigo-isatai-atyrau.e-orda.kz\/ru","isHide":0},{"Id":125,"Type":4,"Name":"Камыскалинский сельский округ","Url":"https:\/\/indigo-kamyskala-atyrau.e-orda.kz\/ru","isHide":0},{"Id":26,"Type":4,"Name":"Караарнинский сельский округ","Url":"https:\/\/indigo-karaarnin-atyrau.e-orda.kz\/ru","isHide":0},{"Id":29,"Type":4,"Name":"Косчагилский сельский округ","Url":"https:\/\/indigo-koschagyl-atyrau.e-orda.kz\/ru","isHide":0},{"Id":3785,"Type":4,"Name":"Майкомгенский сельский округ","Url":"https:\/\/indigo-maikumgen-atyrau.e-orda.kz\/ru","isHide":0},{"Id":126,"Type":4,"Name":"Нарынский сельский округ","Url":"https:\/\/indigo-naryn-atyrau.e-orda.kz\/ru","isHide":0},{"Id":127,"Type":4,"Name":"Тущыкудыкский сельский округ","Url":"https:\/\/indigo-tushykudyk-atyrau.e-orda.kz\/ru","isHide":0}]},{"Name":"Жамбылская область","cities":[{"Id":115,"Type":2,"Name":"г.Тараз","Url":"https:\/\/indigo-taraz.e-orda.kz\/ru","isHide":0},{"Id":3832,"Type":3,"Name":"Байзакский район","Url":"https:\/\/indigo-bayzak.e-orda.kz\/ru","isHide":0},{"Id":3829,"Type":3,"Name":"Жамбылский район","Url":"https:\/\/indigo-zhambyl.e-orda.kz\/ru","isHide":0},{"Id":3833,"Type":3,"Name":"Жуалынский район","Url":"https:\/\/indigo-jualy.e-orda.kz\/ru","isHide":0},{"Id":3830,"Type":3,"Name":"Кордайский район","Url":"https:\/\/indigo-korday.e-orda.kz\/ru","isHide":0},{"Id":3837,"Type":3,"Name":"Меркенский район","Url":"https:\/\/indigo-merke.e-orda.kz\/ru","isHide":0},{"Id":3834,"Type":3,"Name":"Мойынкумский район","Url":"https:\/\/indigo-moiynkum.e-orda.kz\/ru","isHide":0},{"Id":3827,"Type":3,"Name":"район Турара Рыскулова","Url":"https:\/\/indigo-ryskulov.e-orda.kz\/ru","isHide":0},{"Id":3835,"Type":3,"Name":"Сарысуский район","Url":"https:\/\/indigo-sarysu.e-orda.kz\/ru","isHide":0},{"Id":3828,"Type":3,"Name":"Таласский район","Url":"https:\/\/indigo-talas.e-orda.kz\/ru","isHide":0},{"Id":3836,"Type":3,"Name":"Шуский район","Url":"https:\/\/indigo-shu.e-orda.kz\/ru","isHide":0}]},{"Name":"Западно-Казахстанская область","cities":[{"Id":61,"Type":3,"Name":"Бокейординский район","Url":"https:\/\/indigo-bokeyorda.e-orda.kz\/ru","isHide":0}]},{"Name":"Карагандинская область","cities":[{"Id":72,"Type":2,"Name":"г.Абай","Url":"https:\/\/indigo-bilim-otdel.e-orda.kz\/ru","isHide":0},{"Id":73,"Type":2,"Name":"г.Балхаш","Url":"https:\/\/indigo-balkhash.e-orda.kz\/ru","isHide":0},{"Id":76,"Type":2,"Name":"г.Караганда","Url":"https:\/\/indigo-kargoo.e-orda.kz\/ru","isHide":0},{"Id":77,"Type":2,"Name":"г.Приозерск","Url":"https:\/\/indigo-bilim-priozersk.e-orda.kz\/ru","isHide":0},{"Id":78,"Type":2,"Name":"г.Сарань","Url":"https:\/\/indigo-obrazovanie-saran.e-orda.kz\/ru","isHide":0},{"Id":80,"Type":2,"Name":"г.Темиртау","Url":"https:\/\/indigo-ootemirtau.e-orda.kz\/ru","isHide":0},{"Id":81,"Type":2,"Name":"г.Шахтинск","Url":"https:\/\/indigo-shahtinsk-edu.e-orda.kz\/ru","isHide":0},{"Id":1824,"Type":3,"Name":"Абайский район","Url":"https:\/\/indigo-araion.e-orda.kz\/ru","isHide":0},{"Id":3796,"Type":3,"Name":"Актогайский район","Url":"https:\/\/indigo-aktogai.e-orda.kz\/ru","isHide":0},{"Id":3790,"Type":3,"Name":"Бухар-Жырауский район","Url":"https:\/\/indigo-bukhar-zhirau.e-orda.kz\/ru","isHide":0},{"Id":3783,"Type":3,"Name":"Каркаралинский район","Url":"https:\/\/indigo-karkaraly.e-orda.kz\/ru","isHide":0},{"Id":3791,"Type":3,"Name":"Нуринский район","Url":"https:\/\/indigo-nurin.e-orda.kz\/ru","isHide":0},{"Id":3793,"Type":3,"Name":"Осакаровский район","Url":"https:\/\/indigo-osakar.e-orda.kz\/ru","isHide":0},{"Id":3792,"Type":3,"Name":"Шетский район","Url":"https:\/\/indigo-shetsk.e-orda.kz\/ru","isHide":0}]},{"Name":"Костанайская область","cities":[{"Id":3880,"Type":1,"Name":"Подведомственные организации Управления образования Костанайской области","Url":"https:\/\/indigo-kostanai-bilim.e-orda.kz\/ru","isHide":0},{"Id":86,"Type":2,"Name":"г.Аркалык","Url":"https:\/\/indigo-arkalyk-kostanay.e-orda.kz\/ru","isHide":0},{"Id":82,"Type":2,"Name":"г.Костанай","Url":"https:\/\/indigo-kst-goo.e-orda.kz\/ru","isHide":0},{"Id":87,"Type":2,"Name":"г.Лисаковск","Url":"https:\/\/indigo-lsk-kostanay.e-orda.kz\/ru","isHide":0},{"Id":83,"Type":2,"Name":"г.Рудный","Url":"https:\/\/indigo-rudoo.e-orda.kz\/ru","isHide":0},{"Id":88,"Type":3,"Name":"Алтынсаринский район","Url":"https:\/\/indigo-altynsarin-kostanay.e-orda.kz\/ru","isHide":0},{"Id":84,"Type":3,"Name":"Амангельдинский район","Url":"https:\/\/indigo-amangeldy-kostanay.e-orda.kz\/ru","isHide":0},{"Id":90,"Type":3,"Name":"Аулиекольский район","Url":"https:\/\/indigo-aulekol-roo.e-orda.kz\/ru","isHide":0},{"Id":109,"Type":3,"Name":"Денисовский район","Url":"https:\/\/indigo-denroo.e-orda.kz\/ru","isHide":0},{"Id":112,"Type":3,"Name":"Джангелдинский район","Url":"https:\/\/indigo-zhangeldy-kostanay.e-orda.kz\/ru","isHide":0},{"Id":3787,"Type":3,"Name":"Житикаринский район","Url":"https:\/\/indigo-zhitikara-kostanay.e-orda.kz\/ru","isHide":0},{"Id":117,"Type":3,"Name":"Камыстинский район","Url":"https:\/\/indigo-kamisti-kostanay.e-orda.kz\/ru","isHide":0},{"Id":85,"Type":3,"Name":"Карабалыкский район","Url":"https:\/\/indigo-karabalyk-kostanay.e-orda.kz\/ru","isHide":0},{"Id":3879,"Type":3,"Name":"Карасуский район","Url":"https:\/\/indigo-karasu-kostanay.e-orda.kz\/ru","isHide":0},{"Id":111,"Type":3,"Name":"Костанайский район","Url":"https:\/\/indigo-kostregion-kostanay.e-orda.kz\/ru","isHide":0},{"Id":3781,"Type":3,"Name":"Мендыкаринский район","Url":"https:\/\/indigo-mendikara-kostanay.e-orda.kz\/ru","isHide":0},{"Id":3786,"Type":3,"Name":"Наурзумский район","Url":"https:\/\/indigo-naurzum-kostanay.e-orda.kz\/ru","isHide":0},{"Id":89,"Type":3,"Name":"район Беимбета Майлина","Url":"https:\/\/indigo-taran-kostanay.e-orda.kz\/ru","isHide":0},{"Id":118,"Type":3,"Name":"Сарыкольский район","Url":"https:\/\/indigo-sarykol-kostanay.e-orda.kz\/ru","isHide":0},{"Id":121,"Type":3,"Name":"Узункольский район","Url":"https:\/\/indigo-uzunkol-kostanay.e-orda.kz\/ru","isHide":0},{"Id":110,"Type":3,"Name":"Фёдоровский район","Url":"https:\/\/indigo-fedorovka-kostanay.e-orda.kz\/ru","isHide":0}]},{"Name":"Мангистауская область","cities":[{"Id":3860,"Type":2,"Name":"г.Актау","Url":"https:\/\/indigo-aktau.e-orda.kz\/ru","isHide":0},{"Id":3862,"Type":2,"Name":"г.Жанаозен","Url":"https:\/\/indigo-zhanaozen.e-orda.kz\/ru","isHide":0},{"Id":3864,"Type":3,"Name":"Бейнеуский район","Url":"https:\/\/indigo-beyneu.e-orda.kz\/ru","isHide":0},{"Id":3863,"Type":3,"Name":"Каракиянский район","Url":"https:\/\/indigo-karakiya.e-orda.kz\/ru","isHide":0},{"Id":3861,"Type":3,"Name":"Мангистауский район","Url":"https:\/\/indigo-mangystau.e-orda.kz\/ru","isHide":0},{"Id":3865,"Type":3,"Name":"Мунайлинский район","Url":"https:\/\/indigo-munayli.e-orda.kz\/ru","isHide":0},{"Id":3866,"Type":3,"Name":"Тупкараганский район","Url":"https:\/\/indigo-tupkaragan.e-orda.kz\/ru","isHide":0}]},{"Name":"Область Абай","cities":[{"Id":41,"Type":2,"Name":"г.Курчатов","Url":"https:\/\/indigo-kurchatov.e-orda.kz\/ru","isHide":0},{"Id":44,"Type":2,"Name":"г.Семей","Url":"https:\/\/indigo-bilim-semey.e-orda.kz\/ru","isHide":0},{"Id":45,"Type":3,"Name":"Абайский район","Url":"https:\/\/indigo-abay-abo.e-orda.kz\/ru","isHide":0},{"Id":3883,"Type":3,"Name":"Аксуатский район","Url":"https:\/\/indigo-aksuat.e-orda.kz\/ru","isHide":0},{"Id":46,"Type":3,"Name":"Аягозский район","Url":"https:\/\/indigo-ayagoz.e-orda.kz\/ru","isHide":0},{"Id":47,"Type":3,"Name":"Бескарагайский район","Url":"https:\/\/indigo-beskaragay.e-orda.kz\/ru","isHide":0},{"Id":48,"Type":3,"Name":"Бородулихинский район","Url":"https:\/\/indigo-borodulikha.e-orda.kz\/ru","isHide":0},{"Id":3884,"Type":3,"Name":"Жанасемейский район","Url":"https:\/\/indigo-zhanasemey.e-orda.kz\/ru","isHide":0},{"Id":50,"Type":3,"Name":"Жарминский район","Url":"https:\/\/indigo-zharma.e-orda.kz\/ru","isHide":0},{"Id":54,"Type":3,"Name":"Кокпектинский район","Url":"https:\/\/indigo-kokpekti.e-orda.kz\/ru","isHide":0},{"Id":3885,"Type":3,"Name":"Маканчинский район","Url":"https:\/\/indigo-makanshi.e-orda.kz\/ru","isHide":0},{"Id":57,"Type":3,"Name":"Урджарский район","Url":"https:\/\/indigo-urzhar.e-orda.kz\/ru","isHide":0}]},{"Name":"Область Улытау","cities":[{"Id":74,"Type":2,"Name":"г.Жезказган","Url":"https:\/\/indigo-jezkazgan.e-orda.kz\/ru","isHide":0},{"Id":75,"Type":2,"Name":"г.Каражал","Url":"https:\/\/indigo-karazhal-goo.e-orda.kz\/ru","isHide":0},{"Id":79,"Type":2,"Name":"г.Сатпаев","Url":"https:\/\/indigo-satpaev-obr.e-orda.kz\/ru","isHide":0},{"Id":3795,"Type":3,"Name":"Жанааркинский район","Url":"https:\/\/indigo-zhanaarka.e-orda.kz\/ru","isHide":0},{"Id":3794,"Type":3,"Name":"Улытауский район","Url":"https:\/\/indigo-ulytau.e-orda.kz\/ru","isHide":0}]},{"Name":"Павлодарская область","cities":[{"Id":116,"Type":2,"Name":"г.Аксу","Url":"https:\/\/indigo-aksu.e-orda.kz\/ru","isHide":0},{"Id":113,"Type":2,"Name":"г.Павлодар","Url":"https:\/\/indigo-pavlodar.e-orda.kz\/ru","isHide":0},{"Id":3806,"Type":2,"Name":"г.Экибастуз","Url":"https:\/\/indigo-ekibastuz.e-orda.kz\/ru","isHide":0},{"Id":119,"Type":3,"Name":"Актогайский район","Url":"https:\/\/indigo-aktogay-edu.e-orda.kz\/ru","isHide":0},{"Id":3797,"Type":3,"Name":"Баянаульский район","Url":"https:\/\/indigo-bayanaul.e-orda.kz\/ru","isHide":0},{"Id":3798,"Type":3,"Name":"Железинский район","Url":"https:\/\/indigo-zhelezinsk.e-orda.kz\/ru","isHide":0},{"Id":3799,"Type":3,"Name":"Иртышский район","Url":"https:\/\/indigo-irtysh.e-orda.kz\/ru","isHide":0},{"Id":3800,"Type":3,"Name":"Майский район","Url":"https:\/\/indigo-may.e-orda.kz\/ru","isHide":0},{"Id":3801,"Type":3,"Name":"Павлодарский район","Url":"https:\/\/indigo-pavlodar-raion.e-orda.kz\/ru","isHide":0},{"Id":3802,"Type":3,"Name":"район Аққулы","Url":"https:\/\/indigo-akkuly.e-orda.kz\/ru","isHide":0},{"Id":3803,"Type":3,"Name":"район Тереңкөл","Url":"https:\/\/indigo-terenkol.e-orda.kz\/ru","isHide":0},{"Id":3804,"Type":3,"Name":"Успенский район","Url":"https:\/\/indigo-uspenka.e-orda.kz\/ru","isHide":0},{"Id":3805,"Type":3,"Name":"Щербактинский район","Url":"https:\/\/indigo-shcherbakty.e-orda.kz\/ru","isHide":0}]},{"Name":"Северо-Казахстанская область","cities":[{"Id":91,"Type":2,"Name":"г.Петропавловск","Url":"https:\/\/indigo-petropavl.e-orda.kz\/ru","isHide":0},{"Id":94,"Type":3,"Name":"Аккайынский район","Url":"https:\/\/indigo-roo-ak.e-orda.kz\/ru","isHide":0},{"Id":95,"Type":3,"Name":"Есильский район","Url":"https:\/\/indigo-roo-esl.e-orda.kz\/ru","isHide":0},{"Id":100,"Type":3,"Name":"Район имени Габита Мусрепова","Url":"https:\/\/indigo-musrepov.e-orda.kz\/ru","isHide":0}]}];
</script>                                        <nav aria-label="breadcrumb" class="breadcrumbs d-none d-sm-block">
                    <div class="container">
                        <ol class="breadcrumb"><li class="breadcrumb-item"><a href="/ru">Главная</a></li><li class="breadcrumb-item"><a href="/ru/cabinet/request/list">Мои заявления</a></li><li class="breadcrumb-item"><a href="/ru/reserv/get/organization">Выбор дошкольной организации</a></li><li class="breadcrumb-item active" aria-current="page">Выбор группы</li></ol>                    </div>
                </nav>
                        <div class="inner-container-bg">
                                    <div class="container">
                        <header class="body-header">
                                                            <h1 class="body-title">Получить направление </h1>
                                                                                        <p class="body-message-info">Список групп и свободных мест отображается персонально для ИИН: 230409552551</p>
                                                        <a href="#help" class="btn-header-help btn btn-outline-indigo" title="Информация по разделу">Помощь</a>
                        </header>
                    </div>
                
                <div class="position-relative">
                    <!--
<?/*= Html::a($data['Name'], ['/view/' . $data['Id']], ['class' => 'g-list-title', 'data-pjax' => 0]) */?>
    -->
<main class="container">
    <div class="p-t-0 pb-5">
        <div class="row p-1">
            <div class="col-md-6">
                <p><strong>Список групп и свободных мест отображается персонально для ИИН: 230409552551</strong></p>
                <p> ФИО ребенка:   ТУРСЫН АЛДИЯР НАБИЕВИЧ</p>
                <p> Год рождения:  09.04.2023</p>
            </div>
            <div class="col-md-6">
                <h5>КГКП Ясли-сад «Ертегі»</h5>
                <p>Государственная</p>
                <p>город Сатпаев,230732, улица Абая Кунанбаева, здание 60 А</p>
                <hr />
                <a class="btn btn-indigo" href="/ru/view/1" target="_blank">Посмотреть паспорт</a>            </div>
            <div class="container">
                                    <div class="g-callout g-callout-warning">
                        <p>Согласно санитарным нормам количество детей в группе не должно превышать 25 человек. Выбирайте дошкольные организации и группы для зачисления, учитывая фактическое количество детей в них.</p>
                    </div>
                    <div id="view-list" class="g-list-view watermark-background"><div class="garden-group-list dynamic-background" data-key="0">
<h5 class="garden-group-title">Возрастная группа младшая группа (дети 2-х лет)</h5>
<div class="row">
            <div class="col-sm-5 garden-group-block">
            <div class="g-list-item pr-0">
                <div class="d-flex">
                    <div class="g-list-title mb-2 w-100">
                                                    #10544230732:
                        
                        Группа "Күншуақ" кіші топ 2025                    </div>
                    <div class="g-list-count position-relative mb-2">
                                                <div class="g-list-count-block">
                            <span class="g-bg-block g-bg-outline g-bg-outline-grey-500">Общеобразовательная</span>
                                                    </div>
                    </div>
                </div>
                <div class="g-list-body mb-2">
                                        <div>Язык обучения: Казахский </div>
                    <div>Всего свободных мест: 3 </div>
                                            <div>
                                                            <div class="pl-3">
                                    из них 1 в приоритетном доступе до 25.08.2025 07:00                                </div>
                                                            <div class="pl-3">
                                    из них 1 в приоритетном доступе до 15.08.2025 07:00                                </div>
                                                            <div class="pl-3">
                                    из них 1 в приоритетном доступе до 14.08.2025 07:00                                </div>
                                                    </div>
                                        
                    
                    <div>Посещает детей:  <span style="color:black;">16 чел</span></div>
                </div>
                
                    <form id="w0" action="/ru/reserv/get/organization/group/place" method="POST">
<input type="hidden" name="_csrf-frontend" value="GMSIFEK8uIVHTALH_9IKA5bV56umJ7ZaGjPWYSzVEvRX9sx8L_KJ6j0cb6K3jWlx1bK4m8x28BBQRJ4-RYZ7xQ==">
                        <input type="hidden" id="reserveform-requestnumber" name="ReserveForm[requestNumber]" value="927664">                        <input type="hidden" id="reserveform-requestnumberhash" name="ReserveForm[requestNumberHash]">                        <input type="hidden" id="reserveform-groupid" name="ReserveForm[groupId]" value="10544">                        <input type="hidden" id="reserveform-groupidhash" name="ReserveForm[groupIdHash]" value="cbbb18efb6e9789c7b1476058c231bd5">
                        <button type="submit" class="btn btn-indigo ml-auto link-to-disable">Получить направление по заявке №927664</button>                    </form>
                            </div>
        </div>
            <div class="col-sm-5 garden-group-block">
            <div class="g-list-item pr-0">
                <div class="d-flex">
                    <div class="g-list-title mb-2 w-100">
                                                    #10558230732:
                        
                        Группа "Гүлдер" кіші топ 2025 жаңа                    </div>
                    <div class="g-list-count position-relative mb-2">
                                                <div class="g-list-count-block">
                            <span class="g-bg-block g-bg-outline g-bg-outline-grey-500">Общеобразовательная</span>
                                                    </div>
                    </div>
                </div>
                <div class="g-list-body mb-2">
                                        <div>Язык обучения: Казахский </div>
                    <div>Всего свободных мест: 1 </div>
                                            <div>
                                                            <div class="pl-3">
                                    из них 1 в приоритетном доступе до 15.08.2025 07:00                                </div>
                                                    </div>
                                        
                    
                    <div>Посещает детей:  <span style="color:black;">13 чел</span></div>
                </div>
                
                    <form id="w1" action="/ru/reserv/get/organization/group/place" method="POST">
<input type="hidden" name="_csrf-frontend" value="GMSIFEK8uIVHTALH_9IKA5bV56umJ7ZaGjPWYSzVEvRX9sx8L_KJ6j0cb6K3jWlx1bK4m8x28BBQRJ4-RYZ7xQ==">
                        <input type="hidden" id="reserveform-requestnumber" name="ReserveForm[requestNumber]" value="927664">                        <input type="hidden" id="reserveform-requestnumberhash" name="ReserveForm[requestNumberHash]">                        <input type="hidden" id="reserveform-groupid" name="ReserveForm[groupId]" value="10558">                        <input type="hidden" id="reserveform-groupidhash" name="ReserveForm[groupIdHash]" value="129062814faa8bf423948596f415072b">
                        <button type="submit" class="btn btn-indigo ml-auto link-to-disable">Получить направление по заявке №927664</button>                    </form>
                            </div>
        </div>
    </div></div>
</div>                            </div>
        </div>
    </div>
</main>                </div>
            </div>
            <div >
    
    <footer class="footer padding-index position-relative">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6 col-12">
                    <a class="f-logo first-block-logo" href="/ru" aria-label="Indigo">
                        <img class="main-footer-logo" src="/images/spring/logo-footer.png" />
                                            </a>
                </div>
                <div class="col-lg-3 col-md-6 col-12">
                    <a class="f-logo" href="http://e-orda.kz" aria-label="Indig">
                        <img class="main-footer-logo" src="/images/new-year/e-orda-white.svg" />
                                            </a>
                </div>
                <div class="f-warning col-lg-6 col-12">
                    <div class="row">
                        <div class="col-md-6 col-12 f-link">
                            <div class="f-header row">
                                <div class="col-3">
                                    <div class="white-logo__mobile">
                                        <img class="main-footer-logo" src="/images/statistics-icon/icon-6.svg">
                                    </div>
                                                                    </div>
                                <div class="col-9">
                                    <h4>Стоп<br />коррупция</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-12 f-link">
                            <ul class="f-body f-body-stop">
                                <li><a class="" href="/ru/callback-form" data-toggle="modal" data-target="#feedbackModal">Сообщить о коррупции</a></li>
                                <li><a class="" href="/ru/callback-form" data-toggle="modal" data-target="#feedbackModal">Пожаловаться на дошкольную организацию</a></li>
                                <li><a class="" href="/ru/callback-form" data-toggle="modal" data-target="#feedbackModal">Пожаловаться на Управление образования</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <hr />
            <div class="row summer-mb">
                <div class="col-lg-3 col-md-6 col-12 f-link">
                    <div class="f-header">О проекте</div>
                    <ul class="f-body">
                        <li><a href="https://indigo24.kz/ru/faq/43" target="_blank">Назначение портала</a></li>
                        <!-- http://indigo24.kz/faq/tehniceskaa-podderzka/naznacenie-portala -->
                        <!--li></1li-->
                        <li><a href="https://www.instagram.com/bilim_webline/">Новости</a></li>
                        <li><a href="/ru/callback-form" data-toggle="modal" data-target="#feedbackModal">Предложения и замечания</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 col-12 f-link">
                    <div class="f-header">Открытые данные</div>
                    <ul class="f-body">
                                                <li><a href="/ru/information/protocol">Протокол освобождения мест</a></li>
                        <li><a href="/ru/reserv/list">Протокол выдачи направлений</a></li>
                        <li><a href="/ru/queue">Очереди заявлений</a></li>
                                                <li><a href="/ru/information/registry">Свободные места</a><a href="#"></a></li>
                        <li><a href="/ru/statistic/statistic">Статистика работы системы</a></li>

                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 col-12 f-link">
                    <div class="f-header">Полезная информация</div>
                    <ul class="f-body">
                        <li><a href="https://indigo24.kz/ru/faq/19" target="_blank">Как зачислиться в дошкольную организацию</a></li>
                        <li><a href="https://indigo24.kz/ru/faq/48" target="_blank">Как распределяются свободные места</a></li>
                        <li><a href="https://indigo24.kz/ru/faq" target="_blank">Частые вопросы и ответы</a></li>
                        <li><a href="https://indigo24.kz/ru/faq" target="_blank">База знаний</a></li>
                                            </ul>
                </div>
                <div class="col-lg-3 col-md-6 col-12 f-link">
                    <div class="f-header">Техническая поддержка</div>
                    <ul class="f-body">
                        <li><a href="https://indigo24.kz/ru/faq/43" target="_blank">Соглашения об ответственности сторон</a></li>
                        <li><a href="javascript:jivo_api.open()" />Задать вопрос в чате</a></li>
                        <li><a href="https://indigo24.kz/ru/faq/42" target="_blank">Публичная оферта социальных сетей</a></li>
                        <li><a href="https://www.instagram.com/bilim_webline/" target="_blank">Социальные сети</a></li>
                                                                    </ul>
                </div>
            </div>
            <div class="text-center f-copy">
                <p>Интернет-ресурс, его структура, правила форматно-логического контроля и размещенный медиа-контент, охраняется законом «Об авторском праве и смежных правах» Республики Казахстан. ЧУ «КДС-ФРАНЧАЙЗИНГ», 2025©</p>
            </div>
        </div>
    </footer>
    <div class="modal fade" id="feedbackModal" tabindex="-1" role="dialog" aria-labelledby="feedbackModal" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content"></div>
        </div>
    </div>
    <div class="modal" id="defaultModal">
        <div class="modal-dialog">
            <div class="modal-content"></div>
        </div>
    </div>
    <div class="banner-wrap">
                    </div>

    
    </div>

<script>
    var Config = {
        'FILE_MAX_SIZE': 1048576,
        'DigitalContractBannerDisabled': 'hide',
        'IsConfirmRequestPeriod': 'hide',
        'InfoBannerShow': 'hide',
        'OpenJivo': 0,
    };
</script>
            <script>var Messages = {"STEP_3_SET_PARENT_IIN":"Введите ИИН родителя","STEP_3_CHANGE_NUMBER_FILE":"Необходимо заполнить «Свидетельство о рождении».","SCHOOL_LIST_TYPE_MAP":"Карта дошкольных организаций","SCHOOL_LIST_TYPE_TABLE":"Реестр дошкольных организаций","SCHOOL_FREE_TYPE_MAP":"Свободные места","SCHOOL_FREE_TYPE_TABLE":"Свободные места","CHECK_EDIT_REMOVE_FILE":"Вы уверены что хотите удалить информацию о льготе?","CHECK_EDIT_REMOVE_CORRECTIONAL_FILE":"После удаления направления в коррекционную дошкольную организацию, заявление попадет в основную очередь. Удалить направление в коррекционную дошкольную организацию?","SELECT_FILE":"Выбрать файл","EDIT_BUTTON":"Изменить","IIN_VALIDATION":"ИИН должен состоять из 12 цифр","TEMP_DISABLED":"Услуга временно недоступна. Подключение города Нур-Султан еще не завершено. Планируемый срок запуска 10 июня 2019 года.","UPDATE_BENEFITS":"Обновить льготы","SHOW_BENEFITS":"Просмотр льгот","CANCEL_BENEFITS":"Удалить льготу","SAVE_BENEFITS":"Сохранить льготы","CHANGE_BIG_FAMILY":"Изменить","CANCEL_STATEMENT":"Снять заявление с очереди","SHOW_MORE":"Показать еще...","CANCEL":"Отмена","DELETE":"Удалить","NO_SUPPORT":"Support offline message","SUPPORT_OFF":"<div>\n          <h4 class=\"alert-heading\">Извините, Вы обратились в нерабочее время.</h4>\n          <p>График работы технической поддержки с 09:00 - 18:00 в рабочие дни.</p>\n          <hr>\n          <p class=\"mb-0\">После начала рабочего дня обновите страницу и попробуйте еще раз.<br>Спасибо</p>\n        </div>","READ_MORE":"Подробнее","POSTED":"Опубликовано","SOCKET_CONNECT_ERROR":"Ошибка генерации QR-кода","SOCKET_CONNECT_CLOSED":"Генерация QR-кода невозможна по причине окончания периода генерации","INCORRECT_IIN":"Некорректный ИИН","SUPPORT_BUSY":"Извините, все операторы заняты.<br/>Первый освободившийся оператор обязательно свяжется с Вами в ближайшее время","BENEFIT_IS_DELETED_STATUS":"Льгота удалена","BENEFIT_IS_DELETED_MESSAGE":"Отсутствует льгота","REQUEST_ACCESS":"Запросить доступ","REQUEST_DATA":"Запросить данные"}</script>            <div class="alert alert--hide" style="max-height: 85vh; overflow-y: auto;"></div>

            <script src="/assets/939f487/jquery.min.js?v=1681625203"></script>
<script src="/assets/1451fdd5/yii.js?v=1681625203"></script>
<script src="/assets/1451fdd5/yii.activeForm.js?v=1681625203"></script>
<script src="/assets/c9c251a7/js/bootstrap.bundle.js?v=1681625203"></script>
<script src="/js/password-toggle/index.js?v=1698061311"></script>
<script src="/js/jquery.jscrollpane.min.js?v=1674925792"></script>
<script src="/js/odometer.js?v=1674925792"></script>
<script src="/js/slick.min.js?v=1674925792"></script>
<script src="/js/uniform.min.js?v=1674925792"></script>
<script src="/js/jquery.spincrement.min.js?v=1674925792"></script>
<script src="/js/jquery.touchSwipe.min.js?v=1674925792"></script>
<script src="/js/inputmask/jquery.inputmask.bundle.min.js?v=1674925792"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.3.5/jquery.fancybox.min.js"></script>
<script src="/js/jquery.countdown.min.js?v=1674925792"></script>
<script src="/js/site2.js?v=1674925792"></script>
<script src="/js/jquery.nice-select.min.js?v=1674925792"></script>
<script src="/js/app.js?v=1693327486"></script>
<script src="/js/moment/moment.js?v=1674925792"></script>
<script src="/js/moment/moment-ru.js?v=1674925792"></script>
<script src="/js/fancybox/jquery.fancybox.js?v=1674925792"></script>
<script src="/js/fancybox/core.js?v=1674925792"></script>
<script src="/js/shorten.js?v=1674925792"></script>
<script src="/js/city-list.js?v=1674925792"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
<script src="/js/banner-priority.js?v=1699342638"></script>
<script src="/js/scripts.js?v=1745394416"></script>
<script>jQuery(function ($) {
jQuery('#w0').yiiActiveForm([], {"errorSummary":".alert.alert-danger","errorCssClass":"is-invalid","successCssClass":"is-valid","validationStateOn":"input"});
jQuery('#w1').yiiActiveForm([], {"errorSummary":".alert.alert-danger","errorCssClass":"is-invalid","successCssClass":"is-valid","validationStateOn":"input"});
    var lang = $('body').data('lang');
    var city = $('body').data('city');
    var d = new Date(2025,7,14,5,43,35);
    function refreshTime() { $.post('/' + lang + '/gettime', function(data) { var jsd = data; d = new Date(jsd.y, jsd.m, jsd.d, jsd.h, jsd.min, jsd.s); update(); }); }
    refreshTime();
    function update() {
        var month = (d.getMonth()+1) < 10 ? '0' + (d.getMonth()+1) : d.getMonth()+1;
        var day = d.getDate() < 10 ? '0' + d.getDate() : d.getDate();
        var year = d.getFullYear();
        var hours = d.getHours() == 0 ? '00' : d.getHours();
        var minutes = d.getMinutes() < 10 ? '0' + d.getMinutes() : d.getMinutes();
        var seconds = d.getSeconds() < 10 ? '0' + d.getSeconds() : d.getSeconds();
        d.setSeconds(d.getSeconds() + 1);
        $('#clock-text').text((year + '.' + month + '.' + day + ' ' + hours +':' + minutes));
    }
    update();
    window.setInterval(update, 1000); 
    window.setInterval(refreshTime, 1000*60*5);
});</script>        </div>
    </body>
    
    </html>


    <div class="fake-block" data-busy="false">
        <div class="fb-body">
            <div class="fb-title">Техподдержка недоступна</div>
        </div>
    </div>

<style>
    .leaf_baff {
        height: 33px;
        overflow: hidden;
        position: absolute;
        z-index: 6;
        border-radius: 0 24px 0 0!important;
        right: 0;
        top: 0;
        width: 33px;
    }
    .cssLeaf {
        height: 32px;
        width: 32px;
    }
</style>

<div class="video-modal-wrap">
    <div class="modal-block modal-video" data-modal-close>
        <div class="modal-content">
            <div class="modal-body">
                <div class="modal-close" data-modal-close>X</div>
                <iframe width="100%" height="371" frameborder="0" allowfullscreen id="video-iframe"></iframe>
            </div>
        </div>
    </div>
</div>
