package bot

import (
	"fmt"
	"log"
	"time"
)

const (
	LoginPath    = "/ru/cabinet/personal/login"
	PasswordPath = "/ru/cabinet/personal/login/check"
	RequestsPath = "/ru/cabinet/request/list"
	GroupPath    = "/ru/reserv/get/organization/group"
	PlacePath    = "/ru/reserv/get/organization/group/place"
	PlacePathKZ  = "/kz/reserv/get/organization/group/place"
	SubmitPath   = "/ru/reserv/reserv-free-place"
)

// WakeUp sends a request to check if the service is active
func (u *User) WakeUp() error {
	if _, err := u.sendGetRequest(u.BaseURL + RequestsPath); err != nil {
		return fmt.Errorf("something went wrong while waiting: %v", err)
	}
	return nil
}

// <PERSON><PERSON> performs the initial login request
func (u *User) Login() error {
	loginURL := u.BaseURL + LoginPath
	if _, err := u.sendGetRequest(loginURL); err != nil {
		return fmt.Errorf("get request failed: %v", err)
	}

	postData := map[string]string{
		"AccessForm[iin]":               u.Username,
		"AccessForm[registrationAgree]": "1",
		"AccessForm[dataUsageAgree]":    "1",
	}
	if _, err := u.sendPostRequest(loginURL, "", postData); err != nil {
		return fmt.Errorf("post request failed: %v", err)
	}
	return nil
}

// SubmitPassword authenticates the user by submitting their password
func (u *User) SubmitPassword() error {
	pwdURL := u.BaseURL + PasswordPath
	postData := map[string]string{
		"LoginForm[secureKey]": u.Password,
	}

	if _, err := u.sendPostRequest(pwdURL, u.BaseURL+LoginPath, postData); err != nil {
		return fmt.Errorf("incorrect password: %v", err)
	}

	log.Printf("Successfully logged in, User ID: %d - Username: %s", u.ID, u.Username)
	return nil
}

// SubmitPassword authenticates the user by submitting their password
func (u *User) SubmitPasswordWithCode() error {
	sendCode := u.BaseURL + "/ru/cabinet/personal/resend-notification?connect=0"
	u.sendGetRequest(sendCode)

	var verificationCode string
	fmt.Print("Verification Code: ")
	fmt.Scanln(&verificationCode)

	pwdURL := u.BaseURL + PasswordPath
	postData := map[string]string{
		"LoginForm[secureKey]":     u.Password,
		"LoginForm[twoFactorCode]": verificationCode,
	}

	if _, err := u.sendPostRequest(pwdURL, u.BaseURL+LoginPath, postData); err != nil {
		return fmt.Errorf("incorrect password or verification code: %v", err)
	}

	log.Printf("Successfully logged in, User ID: %d - Username: %s", u.ID, u.Username)
	return nil
}

// ParseChildInfo extracts child-related information from the requests page
func (u *User) ParseChildInfo() error {
	requestsURL := u.BaseURL + RequestsPath
	html, err := u.sendGetRequest(requestsURL)
	if err != nil {
		return fmt.Errorf("get request failed: %v", err)
	}

	// Save the HTML response for debugging
	// if err := u.saveResponseToFile("requests-page", html); err != nil {
	// 	u.Logger.Printf("Failed to save requests page: %v", err)
	// }

	// Extract child IIN and request number based on child year
	u.ChildIIN, u.RequestNumber = extractChildInfo([]byte(u.ChildYear), html)
	u.Logger.Printf("ChildYear: %s, Extracted ChildIIN: %s, RequestNumber: %s", u.ChildYear, u.ChildIIN, u.RequestNumber)

	if u.ChildIIN == "" || u.RequestNumber == "" {
		return fmt.Errorf("child IIN or request number not found for child year: %s", u.ChildYear)
	}

	return nil
}

// ParseGroupID fetches the group ID for the child
func (u *User) ParseGroupID() error {
	groupURL := u.BaseURL + GroupPath
	hashedGardenID := generateMD5(u.GardenID)

	formData := map[string]string{
		"ReserveForm[gardenId]":     u.GardenID,
		"ReserveForm[gardenIdHash]": hashedGardenID,
		"ReserveForm[iin]":          u.ChildIIN,
	}

	html, err := u.sendPostRequest(groupURL, groupURL, formData)
	if err != nil {
		return fmt.Errorf("couldn't access /group page: %s %v", time.Now().Format("2006-01-02 15:04:05.000"), err)
	}

	u.GroupID, _ = extractGroupID(html)

	log.Printf("Group ID successfully parsed at %s", time.Now().Format("2006-01-02 15:04:05.000"))

	return u.saveResponseToFile("group-ids", html)
}

// BookPlace attempts to book a place for the child
func (u *User) BookPlace() error {
	formData := map[string]string{
		"ReserveForm[requestNumber]":     u.RequestNumber,
		"ReserveForm[requestNumberHash]": generateMD5(u.RequestNumber),
		"ReserveForm[groupId]":           u.GroupID,
		"ReserveForm[groupIdHash]":       generateMD5(u.GroupID),
		"ReserveForm[iin]":               u.ChildIIN,
	}

	fmt.Println("BOOKING DATA:", formData)

	// Attempt first booking
	if html, err := u.tryBooking(u.BaseURL+PlacePathKZ, formData); err == nil {
		return u.finalizeBooking(html)
	}

	// Retry with alternate path
	log.Printf("User-%d, First attempt failed at %s: Retrying with alternate path...\n", u.ID, time.Now().Format("2006-01-02 15:04:05.000"))

	html, err := u.tryBooking(u.BaseURL+PlacePath, formData)
	if err != nil {
		log.Printf("User-%d: Үлгермедік :( %s: %v", u.ID, time.Now().Format("2006-01-02 15:04:05.000"), err)
		return err
	}

	return u.finalizeBooking(html)
}

// tryBooking attempts to book a place and returns the response HTML
func (u *User) tryBooking(url string, formData map[string]string) ([]byte, error) {
	html, err := u.sendPostRequest(url, url, formData)
	if err != nil {
		log.Printf("User-%d, Booking attempt failed at %s: %v", u.ID, time.Now().Format("2006-01-02 15:04:05.000"), err)
		return nil, err
	}
	return html, nil
}

// finalizeBooking extracts final data and confirms the booking
func (u *User) finalizeBooking(html []byte) error {
	log.Printf("User-%d: 🎉 Алақай, орын алынды!", u.ID)
	respData := extractFinalPage(html)
	if respData == nil {
		return fmt.Errorf("failed to extract final page data")
	}

	return u.ConfirmPlace(respData)
}

// ConfirmPlace finalizes the reservation by submitting the form
func (u *User) ConfirmPlace(placeData [][]byte) error {
	if placeData == nil {
		return fmt.Errorf("failed to retrieve final step page data")
	}

	submitURL := u.BaseURL + SubmitPath
	postData := map[string]string{
		"ReservationForm[name]":              string(placeData[0]),
		"ReservationForm[kindergarden_type]": string(placeData[1]),
		"ReservationForm[group_name]":        string(placeData[2]),
		"ReservationForm[group_type]":        string(placeData[3]),
		"ReservationForm[age_group]":         string(placeData[4]),
		"ReservationForm[study_language]":    string(placeData[5]),
		"ReservationForm[iin]":               string(placeData[6]),
		"ReservationForm[groupId]":           string(placeData[7]),
		"ReservationForm[requestNumber]":     string(placeData[8]),
		"ReserveForm[finalHash]":             string(placeData[9]),
		"ReservationForm[ruleAgree]":         "1",
	}

	u.Client.SetCookie("Upgrade-Insecure-Requests", "1")

	// Avoid hammering the server
	log.Println("Waiting 5 seconds before confirming the place...")
	time.Sleep(5 * time.Second)

	if _, err := u.sendPostRequest(submitURL, "", postData); err != nil {
		return fmt.Errorf("place confirmation failed: %w", err)
	}

	log.Println("✅ Place successfully confirmed!")
	return nil
}
