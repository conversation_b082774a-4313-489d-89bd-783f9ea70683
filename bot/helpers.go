package bot

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"log"
	"math/rand"
	"os"
	"time"
)

func setupUserLogger(userID int) (*os.File, *log.Logger, error) {
	logFileName := fmt.Sprintf("logs/user_%d.log", userID) // Unique log per user
	logFile, err := os.OpenFile(logFileName, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0666)
	if err != nil {
		return nil, nil, err
	}

	logger := log.New(logFile, "", log.Ldate|log.Ltime|log.Lmicroseconds|log.Lshortfile)
	return logFile, logger, nil
}

func randomUserAgent() string {
	userAgents := []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15",
		"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.111 Safari/537.36",
		"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1",
	}
	source := rand.NewSource(time.Now().UnixNano())
	r := rand.New(source)
	userAgent := userAgents[r.Intn(len(userAgents))]
	fmt.Println("Random User-Agent:", userAgent)
	return userAgent
}

func isRedirectStatusCode(statusCode int) bool {
	return statusCode == 301 || statusCode == 302 || statusCode == 303 || statusCode == 307 || statusCode == 308
}

func extractCSRFToken(htmlBody []byte) string {
	csrfStart := bytes.Index(htmlBody, []byte(`name="_csrf-frontend"`))
	if csrfStart == -1 {
		return ""
	}
	valueStart := bytes.Index(htmlBody[csrfStart:], []byte(`value="`))
	if valueStart == -1 {
		return ""
	}
	valueStart += csrfStart + len(`value="`)
	valueEnd := bytes.Index(htmlBody[valueStart:], []byte(`"`))
	if valueEnd == -1 {
		return ""
	}
	csrfToken := htmlBody[valueStart : valueStart+valueEnd]
	return string(csrfToken)
}

func extractChildInfo(iinPrefix, html []byte) (string, string) {
	iinKey := []byte(`name="ReserveForm[iin]" value="`)
	reqNumKey := []byte(`name="ReserveForm[requestNumber]" value="`)

	var iin, requestNumber []byte
	var startIndex, endIndex int
	foundCount := 0

	fmt.Printf("Looking for IINs starting with: %s\n", string(iinPrefix))

	for {
		startIndex = bytes.Index(html, iinKey)
		if startIndex == -1 {
			break
		}
		startIndex += len(iinKey)
		endIndex = bytes.IndexByte(html[startIndex:], '"') + startIndex
		iin = html[startIndex:endIndex]
		foundCount++
		fmt.Printf("Found IIN #%d: %s\n", foundCount, string(iin))

		if bytes.HasPrefix(iin, iinPrefix) {
			fmt.Printf("IIN %s matches prefix %s\n", string(iin), string(iinPrefix))
			reqStartIndex := bytes.Index(html[endIndex:], reqNumKey)
			if reqStartIndex != -1 {
				reqStartIndex += endIndex + len(reqNumKey)
				reqEndIndex := bytes.IndexByte(html[reqStartIndex:], '"') + reqStartIndex
				requestNumber = html[reqStartIndex:reqEndIndex]
				fmt.Printf("Found requestNumber: %s, iin: %s\n", requestNumber, iin)
				return string(iin), string(requestNumber)
			} else {
				fmt.Printf("No request number found for IIN: %s\n", string(iin))
			}
		} else {
			fmt.Printf("IIN %s does not match prefix %s\n", string(iin), string(iinPrefix))
		}
		html = html[endIndex:]
	}

	return "", ""
}

func extractGroupID(html []byte) (string, int) {
	data := []byte(`name="ReserveForm[groupId]" value="`)
	var endIndex int
	startIndex := bytes.Index(html, data)
	if startIndex == -1 {
		return "", startIndex
	} else {
		startIndex += len(data)
		endIndex = bytes.IndexByte(html[startIndex:], '"') + startIndex
		data = html[startIndex:endIndex]
	}
	return string(data), endIndex
}

func extractFinalPage(html []byte) [][]byte {
	datas := [][]byte{
		[]byte(`name="ReservationForm[name]" value="`),
		[]byte(`name="ReservationForm[kindergarden_type]" value="`),
		[]byte(`name="ReservationForm[group_name]" value="`),
		[]byte(`name="ReservationForm[group_type]" value="`),
		[]byte(`name="ReservationForm[age_group]" value="`),
		[]byte(`name="ReservationForm[study_language]" value="`),
		[]byte(`name="ReservationForm[iin]" value="`),
		[]byte(`name="ReservationForm[groupId]" value="`),
		[]byte(`name="ReservationForm[requestNumber]" value="`),
		[]byte(`name="ReserveForm[finalHash]" value="`),
	}
	var startIndex, endIndex int
	for i := 0; i < 10; i++ {
		startIndex = bytes.Index(html[endIndex:], datas[i])
		if startIndex == -1 {
			return nil
		} else {
			startIndex += endIndex + len(datas[i])
			endIndex = bytes.IndexByte(html[startIndex:], '"') + startIndex
			datas[i] = html[startIndex:endIndex]
		}
	}

	return datas
}

func generateMD5(inputStr string) string {
	hash := md5.Sum([]byte(inputStr))
	return hex.EncodeToString(hash[:])
}
