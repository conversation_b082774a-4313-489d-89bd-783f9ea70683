package bot

import (
	"fmt"
	"log"
	"os"

	"github.com/valyala/fasthttp"
)

type HTTPClient interface {
	Do(req *fasthttp.Request, resp *fasthttp.Response) error
	NewGetRequest(url string) *fasthttp.Request
	NewPostRequest(url string, formData map[string]string) *fasthttp.Request
	SetCookie(name, value string)
	GetCookie(name string) (string, bool)
	SetCSRFToken(token string)
	GetCSRFToken() string
}

// Config represents the top-level structure of users.json
type Config struct {
	BaseURL      string `json:"base_url"`
	TargetHour   int    `json:"target_hour"`
	TargetMinute int    `json:"target_minute"`
	TargetSecond int    `json:"target_second"`
	Users        []User `json:"users"`
}

type User struct {
	ID            int         `json:"-"` // User index
	Comment       string      `json:"comment"`
	Username      string      `json:"login"`
	Password      string      `json:"password"`
	ChildYear     string      `json:"child_year"`
	GardenID      string      `json:"garden_id"`
	GroupID       string      `json:"group_id"`
	ChildIIN      string      `json:"-"` // Extracted from requests page
	RequestNumber string      `json:"-"` // Extracted from requests page
	BaseURL       string      `json:"-"` // Will be set from config
	Client        HTTPClient  `json:"-"`
	Logger        *log.Logger `json:"-"`
	LogFile       *os.File    `json:"-"`
}

func (u *User) updateCSRFToken(resp *fasthttp.Response) {
	if csrfToken := extractCSRFToken(resp.Body()); csrfToken != "" {
		u.Client.SetCSRFToken(csrfToken)
	}
}

func (u *User) sendGetRequest(url string) ([]byte, error) {
	req := u.Client.NewGetRequest(url)
	defer fasthttp.ReleaseRequest(req)
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	if err := u.Client.Do(req, resp); err != nil {
		return nil, fmt.Errorf("request failed: %v", err)
	}

	if resp.StatusCode() != fasthttp.StatusOK {
		return nil, fmt.Errorf("failed with status code: %d", resp.StatusCode())
	}
	u.updateCSRFToken(resp)
	return resp.Body(), nil
}

func (u *User) sendPostRequest(url, expectedURL string, formData map[string]string) ([]byte, error) {
	req := u.Client.NewPostRequest(url, formData)
	defer fasthttp.ReleaseRequest(req)
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	if err := u.Client.Do(req, resp); err != nil {
		return nil, fmt.Errorf("POST request to %s failed: %w", url, err)
	}

	if resp.StatusCode() != fasthttp.StatusOK {
		return nil, fmt.Errorf("unexpected status %d from %s", resp.StatusCode(), url)
	}

	if expectedURL != "" {
		if location := req.URI().String(); location != expectedURL {
			return nil, fmt.Errorf("unexpected redirect location: got %s, want %s", location, expectedURL)
		}
	}

	u.updateCSRFToken(resp)
	return resp.Body(), nil
}

// saveResponseToFile writes API responses to a file for debugging
func (u *User) saveResponseToFile(fileName string, data []byte) error {
	filePath := fmt.Sprintf("responses/%d-%s.html", u.ID, fileName)

	// Create responses directory if it doesn't exist
	if err := os.MkdirAll("responses", 0755); err != nil {
		return fmt.Errorf("failed to create responses directory: %w", err)
	}

	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("failed to create file %s: %w", filePath, err)
	}
	defer file.Close()

	if _, err := file.Write(data); err != nil {
		return fmt.Errorf("failed to write to file %s: %w", filePath, err)
	}

	return nil
}

func (u *User) CloseLogger() {
	if u.LogFile != nil {
		u.LogFile.Close()
	}
}
