package bot

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"os"
	"sync"
	"time"

	"github.com/valyala/fasthttp"
)

// StartBot calls functions. It is concurrently safe.
func StartBot() {
	file, err := os.ReadFile("./users.json")
	if err != nil {
		log.Fatalf("Error reading config file: %v", err)
	}
	var config Config
	if err := json.Unmarshal(file, &config); err != nil {
		log.Fatalf("Error parsing JSON data: %v", err)
	}

	// Configure the HTTP client
	host := config.BaseURL[8:] // f.e. "indigo-jezkazgan.e-orda.kz"
	hostClient := &fasthttp.HostClient{
		Addr:                host + ":443",
		IsTLS:               true,
		MaxConns:            20,
		MaxIdleConnDuration: 100 * time.Second,
		TLSConfig: &tls.Config{
			ServerName:         host,
			InsecureSkipVerify: true, // Disable for testing
		},
		Dial: func(addr string) (net.Conn, error) {
			return fasthttp.DialTimeout(addr, 15*time.Second)
		},
	}

	// Initialize all users
	for i := range config.Users {
		config.Users[i].ID = i + 1
		config.Users[i].BaseURL = config.BaseURL

		logFile, logger, err := setupUserLogger(config.Users[i].ID)
		if err != nil {
			log.Fatalf("Error setting up logger for user %d: %v", config.Users[i].ID, err)
		}
		config.Users[i].LogFile = logFile
		config.Users[i].Logger = logger
		config.Users[i].Client = NewCookieClient(hostClient)
	}

	// Pre-authenticate all users before target time with 25ms delays
	log.Println("Starting pre-authentication of all users...")
	for i := range config.Users {
		log.Printf("Authenticating user %d: %s", config.Users[i].ID, config.Users[i].Username)
		if err := PreAuthenticateUser(&config.Users[i], hostClient); err != nil {
			log.Printf("Pre-authentication failed for user %d - %s: %v", config.Users[i].ID, config.Users[i].Username, err)
		}

		// Add 25ms delay between users (except for the last user)
		if i < len(config.Users)-1 {
			time.Sleep(25 * time.Millisecond)
		}
	}

	// Wait until target time
	log.Println("Waiting until target time...")
	WaitUntilMachineTime(config.TargetHour, config.TargetMinute, config.TargetSecond)

	// Parse group IDs if needed and book places concurrently with staggered starts
	log.Println("Starting booking process...")
	var wg sync.WaitGroup
	errCh := make(chan error, len(config.Users))

	for i := range config.Users {
		wg.Add(1)
		go func(u *User, index int) {
			defer wg.Done()
			defer u.CloseLogger()

			// Stagger the start times by 25ms between users
			time.Sleep(time.Duration(index) * 20 * time.Millisecond)

			log.Printf("Processing user %d: %s", u.ID, u.Username)
			if err := FinalizeBooking(u, hostClient); err != nil {
				errCh <- fmt.Errorf("user %d - %s failed: %v", u.ID, u.Username, err)
			}
		}(&config.Users[i], i)
	}

	wg.Wait()
	close(errCh)

	// Handle errors
	for err := range errCh {
		log.Printf("Error: %v", err)
	}
}

// PreAuthenticateUser performs login and password submission for a user
func PreAuthenticateUser(u *User, hc *fasthttp.HostClient) error {
	if err := u.Login(); err != nil {
		return fmt.Errorf("Login ❌ %v", err)
	}
	if err := u.SubmitPasswordWithCode(); err != nil {
		return fmt.Errorf("SubmitPassword ❌ %v", err)
	}
	if err := u.ParseChildInfo(); err != nil {
		return fmt.Errorf("ParseChildInfo ❌ %v", err)
	}
	// Parse group ID if time is before 6:30 AM
	sixThirty := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 6, 30, 0, 0, time.Local)
	if u.GroupID == "" && time.Now().Before(sixThirty) {
		if err := u.ParseGroupID(); err != nil {
			return fmt.Errorf("ParseGroupID ❌ %v", err)
		}
	}
	return nil
}

// FinalizeBooking handles group ID parsing and booking for a user
func FinalizeBooking(u *User, hc *fasthttp.HostClient) error {
	// Parse group ID if not provided
	if u.GroupID == "" {
		if err := u.ParseGroupID(); err != nil {
			return fmt.Errorf("ParseGroupID ❌ %v", err)
		}
	}

	// Book the place
	if err := u.BookPlace(); err != nil {
		return fmt.Errorf("BookPlace ❌ %v", err)
	}
	return nil
}
