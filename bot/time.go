package bot

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"
)

const Millisecond = 100_000_000

// TimeData структура для разбора JSON-ответа от сервера
type TimeData struct {
	H   int `json:"h"`
	Min int `json:"min"`
	S   int `json:"s"`
}

// GetEstimatedServerTime получает текущее время сервера с поправкой на задержку сети.
// Эта функция объединяет вашу логику GetServerTime и начальную часть CalculateTimeDrift.
func GetEstimatedServerTime(baseURL string) (time.Time, error) {
	// Замеряем время до и после запроса для расчета задержки
	localBefore := time.Now()
	resp, err := http.Get(baseURL + "/gettime")
	if err != nil {
		return time.Time{}, fmt.Errorf("не удалось получить время с сервера: %w", err)
	}
	defer resp.Body.Close()
	localAfter := time.Now()

	// Вычисляем полную задержку (Round Trip Time)
	rtt := localAfter.Sub(localBefore)
	// Предполагаем, что задержка в одну сторону - это половина полной задержки
	latency := rtt / 2

	// Декодируем ответ сервера
	var timeData TimeData
	if err := json.NewDecoder(resp.Body).Decode(&timeData); err != nil {
		return time.Time{}, fmt.Errorf("не удалось декодировать JSON: %w", err)
	}

	now := time.Now()
	// Собираем время, полученное от сервера
	serverTime := time.Date(
		now.Year(), now.Month(), now.Day(),
		timeData.H, timeData.Min, timeData.S,
		0, // Наносекунды изначально 0
		now.Location(),
	)

	// Оцениваем реальное время на сервере в момент получения ответа,
	// прибавляя задержку на передачу данных.
	estimatedServerTime := serverTime.Add(latency)

	return estimatedServerTime, nil
}

// WaitForTargetTimeAdaptive реализует новую стратегию адаптивного ожидания.
func WaitForTargetTimeAdaptive(baseURL string, targetHour, targetMin, targetSec int) error {
	log.Println("Запуск адаптивного ожидания...")

	// 1. Определяем целевое время сегодня
	now := time.Now()
	targetTime := time.Date(
		now.Year(), now.Month(), now.Day(),
		targetHour, targetMin, targetSec,
		// 0, // Целимся ровно в начало секунды
		Millisecond, // Целимся в середину целевой секунды
		now.Location(),
	)

	// Основной цикл адаптивного ожидания
	for {
		// 2. Получаем текущее оценочное время сервера
		estimatedServerTime, err := GetEstimatedServerTime(baseURL)
		if err != nil {
			log.Printf("Предупреждение: не удалось получить время сервера, повтор через 1с. Ошибка: %v", err)
			time.Sleep(1 * time.Second)
			continue // Пропускаем итерацию и пробуем снова
		}

		// 3. Вычисляем, сколько времени осталось ждать
		timeToWait := targetTime.Sub(estimatedServerTime)

		log.Printf(
			"Текущее время сервера (оценка): %s. Осталось ждать: %v",
			estimatedServerTime.Format("15:04:05.000"),
			timeToWait,
		)

		// 4. Проверяем, не прошло ли уже время
		if timeToWait <= 0 {
			log.Printf("Целевое время достигнуто или уже прошло (опоздание: %v)", -timeToWait)
			break // Выходим из цикла для завершения
		}

		// 5. Выбираем длительность сна в зависимости от оставшегося времени
		var sleepDuration time.Duration
		if timeToWait > 1*time.Minute {
			// Если ждать долго, проверяем реже
			sleepDuration = 10 * time.Second
		} else if timeToWait > 5*time.Second {
			// По мере приближения проверяем чаще
			sleepDuration = 1 * time.Second
		} else {
			// Когда совсем близко, проверяем очень часто
			sleepDuration = 200 * time.Millisecond
		}

		// Не спим дольше, чем нужно
		if sleepDuration > timeToWait {
			sleepDuration = timeToWait / 2
			if sleepDuration < 100*time.Millisecond {
				sleepDuration = 100 * time.Millisecond
			}
		}

		// 6. Уходим в сон
		log.Printf("Следующая проверка через %v", sleepDuration)
		time.Sleep(sleepDuration)
	}

	// 7. Финальное точное ожидание (Busy Wait)
	// Мы вышли из цикла, значит, время очень близко.
	// Теперь используем локальные часы для сверхточного ожидания последних миллисекунд.
	// Рассчитаем смещение еще раз, чтобы оно было максимально свежим.
	finalServerTime, err := GetEstimatedServerTime(baseURL)
	if err != nil {
		log.Println("Не удалось получить финальное время, действуем немедленно.")
		return nil
	}

	// finalDrift := finalServerTime.Sub(time.Now())
	finalDrift := time.Until(finalServerTime)
	adjustedTargetTime := targetTime.Add(-finalDrift)

	log.Printf("Финальный отсчет... Цель (с поправкой): %s", adjustedTargetTime.Format("15:04:05.000"))

	for time.Now().Before(adjustedTargetTime) {
		// Почти не спим, чтобы не пропустить момент
		time.Sleep(time.Microsecond)
	}

	log.Printf("ВРЕМЯ НАСТАЛО! Действуем сейчас: %s", time.Now().Format("15:04:05.000"))

	// Здесь должен быть ваш код для отправки запроса

	// accuracy := time.Now().Sub(adjustedTargetTime)
	accuracy := time.Since(adjustedTargetTime)
	log.Printf("Точность попадания: %v", accuracy)

	return nil
}

// WaitUntilMachineTime waits until the machine time reaches the specified target time
func WaitUntilMachineTime(hour, minute, second int) error {
	now := time.Now()
	targetTime := time.Date(now.Year(), now.Month(), now.Day(), hour, minute, second, 0, now.Location())
	timeToWait := targetTime.Sub(now)
	fmt.Printf("Target time: %v\nMachine time: %v\n",
		targetTime, now)

	if timeToWait > 0 {
		fmt.Printf("Sleeping for %.2f seconds...\n", timeToWait.Seconds())
		time.Sleep(timeToWait)
	} else {
		fmt.Println("Target time already passed, no need to wait.")
	}

	return nil
}
