package bot

import (
	"bytes"
	"fmt"
	"sync"
	"time"

	"github.com/valyala/fasthttp"
)

type CookieClient struct {
	hc        *fasthttp.HostClient
	cookieJar *sync.Map
	userAgent string
	csrfToken string
	csrfMu    sync.Mutex // Mutex for CSRF token
}

func NewCookieClient(hc *fasthttp.HostClient) *CookieClient {
	return &CookieClient{
		hc:        hc,
		cookieJar: &sync.Map{},
		userAgent: randomUserAgent(),
	}
}

// SetCookie stores a cookie (thread-safe via sync.Map).
func (cl *CookieClient) SetCookie(name, value string) {
	cl.cookieJar.Store(name, value)
}

// Get<PERSON><PERSON>ie retrieves a cookie (thread-safe via sync.Map).
func (cl *CookieClient) GetCookie(name string) (string, bool) {
	val, ok := cl.cookieJar.Load(name)
	if !ok {
		return "", false
	}
	return val.(string), true // Type assertion (ensure values are strings)
}

// SetCSRFToken sets the CSRF token (thread-safe).
func (cl *CookieClient) SetCSRFToken(token string) {
	cl.csrfMu.Lock()
	defer cl.csrfMu.Unlock()
	cl.csrfToken = token
}

// GetCSRFToken returns the CSRF token (thread-safe).
func (cl *CookieClient) GetCSRFToken() string {
	cl.csrfMu.Lock()
	defer cl.csrfMu.Unlock()
	return cl.csrfToken
}

// Do executes an HTTP request and manages cookies.
func (cl *CookieClient) Do(req *fasthttp.Request, resp *fasthttp.Response) error {
	for {
		if err := cl.hc.Do(req, resp); err != nil {
			return err
		}
		resp.Header.VisitAllCookie(func(key, value []byte) {
			c := fasthttp.AcquireCookie()
			defer fasthttp.ReleaseCookie(c)
			// Parse cookie from raw Set-Cookie header value
			if err := c.ParseBytes(value); err != nil {
				return // Skip invalid cookies
			}
			cookieName := string(key)
			cookieValue := string(c.Value())
			if expire := c.Expire(); expire != fasthttp.CookieExpireUnlimited && expire.Before(time.Now()) {
				req.Header.DelCookieBytes(key)
				cl.cookieJar.Delete(cookieName)
			} else {
				cl.cookieJar.Store(cookieName, cookieValue)
			}
		})

		// Check for redirect
		if statusCode := resp.Header.StatusCode(); !isRedirectStatusCode(statusCode) {
			break
		}

		location := resp.Header.PeekBytes([]byte("Location"))
		if len(location) == 0 {
			return fmt.Errorf("redirect with missing Location header")
		}

		if bytes.HasPrefix(location, []byte("http:")) {
			location = append([]byte("https://"), location[7:]...) // Skip "http://"
		}

		u := req.URI()
		u.UpdateBytes(location)
		resp.Reset()
	}
	return nil
}

func (cl *CookieClient) NewGetRequest(url string) *fasthttp.Request {
	req := fasthttp.AcquireRequest()
	req.SetRequestURI(url)
	req.Header.SetMethod(fasthttp.MethodGet)
	req.Header.SetUserAgent(cl.userAgent)
	cl.cookieJar.Range(func(key, value interface{}) bool {
		req.Header.SetCookie(key.(string), value.(string))
		return true
	})

	return req
}

func (cl *CookieClient) NewPostRequest(url string, formData map[string]string) *fasthttp.Request {
	req := fasthttp.AcquireRequest()
	req.SetRequestURI(url)
	req.Header.SetMethod(fasthttp.MethodPost)
	req.Header.SetUserAgent(cl.userAgent)
	req.Header.SetContentType("application/x-www-form-urlencoded")
	// Auto-inject CSRF token if present
	if csrfToken := cl.GetCSRFToken(); csrfToken != "" {
		formData["_csrf-frontend"] = csrfToken
	}
	// Build form data
	args := fasthttp.AcquireArgs()
	for key, value := range formData {
		args.Add(key, value)
	}
	args.WriteTo(req.BodyWriter())
	fasthttp.ReleaseArgs(args)
	// Inject cookies
	cl.cookieJar.Range(func(key, value interface{}) bool {
		req.Header.SetCookie(key.(string), value.(string))
		return true
	})
	return req
}
